import type { Metadata } from 'next'
import { NPIHeroComponent } from '@/heros/NPIHero'
import {
  NPIIntroductionBlock,
  NPIFeaturedProjectsBlock,
  NPISuccessStoriesBlock,
  NPILatestUpdatesBlock,
} from '@/blocks/pages/home'
import { NPIStatisticsBlock } from '@/blocks/shared'
import PageClient from './[slug]/page.client'

export default function HomePage() {
  return (
    <article className="min-h-screen">
      <PageClient />

      {/* Hero Section */}
      <NPIHeroComponent />

      {/* Main Content Sections */}
      <main className="relative">
        {/* Introduction Section */}
        <section className="pt-0 pb-2 lg:pb-4 relative min-h-[400px] lg:min-h-[500px] flex items-center bg-[#E5E1DC]">
          <div className="w-full">
            <NPIIntroductionBlock />
          </div>
        </section>

        {/* Statistics Section */}
        <section className="pt-2 lg:pt-4 pb-2 lg:pb-4 relative min-h-[400px] lg:min-h-[500px] flex items-center bg-[#CABA9C]/20">
          <div className="w-full">
            <NPIStatisticsBlock />
          </div>
        </section>

        {/* Featured Projects Section */}
        <section className="pt-2 lg:pt-4 pb-2 lg:pb-4 relative min-h-[400px] lg:min-h-[500px] flex items-center bg-[#4C6444]/12">
          <div className="w-full">
            <NPIFeaturedProjectsBlock />
          </div>
        </section>

        {/* Success Stories Section */}
        <section className="pt-2 lg:pt-4 pb-2 lg:pb-4 relative min-h-[400px] lg:min-h-[500px] flex items-center bg-[#2F2C29]/8">
          <div className="w-full">
            <NPISuccessStoriesBlock />
          </div>
        </section>

        {/* Latest Updates Section */}
        <section className="pt-2 lg:pt-4 pb-0 relative min-h-[400px] lg:min-h-[500px] flex items-center bg-[#E5E1DC]">
          <div className="w-full">
            <NPILatestUpdatesBlock />
          </div>
        </section>
      </main>
    </article>
  )
}

export async function generateMetadata(): Promise<Metadata> {
  return {
    title:
      'Natural Products Industry Initiative - Kenya | Harnessing Indigenous Wealth for Sustainable Growth',
    description:
      "Transforming Kenya's Vision 2030 into reality through sustainable natural products development. Harnessing indigenous knowledge and natural resources for economic development.",
    openGraph: {
      title: 'Natural Products Industry Initiative - Kenya',
      description:
        'Harnessing Indigenous Wealth for Sustainable Growth - Natural Products Industry Initiative Kenya',
      type: 'website',
      url: '/',
    },
  }
}
